<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دامج ومجزئ ملفات JSON</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>دامج ومجزئ ملفات JSON</h1>

        <div class="tabs">
            <button class="tab-btn active" id="combine-tab-btn">دمج الملفات</button>
            <button class="tab-btn" id="split-tab-btn">تجزئة الملفات</button>
            <button class="tab-btn" id="analyze-tab-btn">تحليل الملف</button>
        </div>

        <div class="tab-content" id="combine-tab">
            <div class="upload-section">
                <label for="json-files">اختر ملفات JSON للدمج:</label>
                <input type="file" id="json-files" accept=".json" multiple>
                <button id="combine-btn">دمج الملفات</button>
            </div>
        </div>

        <div class="tab-content" id="split-tab" style="display: none;">
            <div class="upload-section">
                <label for="json-file-to-split">اختر ملف JSON للتجزئة:</label>
                <input type="file" id="json-file-to-split" accept=".json">
                <div class="parts-selector">
                    <label for="parts-count">عدد الأجزاء (1-10):</label>
                    <input type="number" id="parts-count" min="1" max="10" value="2">
                </div>
                <button id="split-btn">تجزئة الملف</button>
            </div>
        </div>

        <div class="tab-content" id="analyze-tab" style="display: none;">
            <div class="upload-section">
                <label for="json-file-to-analyze">اختر ملف JSON للتحليل:</label>
                <input type="file" id="json-file-to-analyze" accept=".json">
                <button id="analyze-btn">تحليل الملف</button>
            </div>
        </div>

        <div class="result-section" id="result-section">
            <h2>النتيجة</h2>
            <div class="stats" id="stats"></div>
            <pre id="preview"></pre>
            <div class="download-buttons">
                <button id="download-btn" disabled>تحميل الملف المدمج</button>
                <div id="split-download-container" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

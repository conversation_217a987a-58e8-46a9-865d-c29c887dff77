* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

/* Tabs */
.tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    padding: 10px 20px;
    margin: 0 5px;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
}

.tab-btn:hover {
    background-color: #e9e9e9;
}

.tab-btn.active {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

.tab-content {
    margin-bottom: 20px;
}

.upload-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.parts-selector {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

input[type="file"] {
    display: block;
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

input[type="number"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 10px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #45a049;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.result-section {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.stats {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #e9f7ef;
    border-radius: 4px;
}

pre {
    background-color: #f1f1f1;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.download-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#download-btn {
    margin-bottom: 10px;
}

#split-download-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.split-download-btn {
    margin: 5px;
}

.error {
    color: #d9534f;
    background-color: #f9eaea;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}
